# 云函数调用次数优化方案

## 📊 **问题分析**

### **当前状况**
- **本月调用次数**: 8.22万次（已用41%配额）
- **本日调用次数**: 637次
- **问题严重性**: 按当前趋势，月底可能超出20万次配额

### **什么是"调用次数"？**
调用次数包括所有对微信云开发服务的请求：
1. **云函数调用** (`wx.cloud.callFunction`)
2. **数据库操作** (`.get()`, `.add()`, `.update()`, `.remove()`, `.count()`)
3. **云存储操作** (`wx.cloud.getTempFileURL`, `wx.cloud.uploadFile`)

## 🔍 **发现的主要问题**

### **1. 缺失的定时器配置**
**问题**: `notificationScheduler`云函数中有`courseReminder`逻辑，但配置文件中缺少对应的定时器。

**解决方案**: ✅ 已修复
```json
{
  "name": "courseReminder",
  "type": "timer", 
  "config": "0 0 */1 * * *",
  "desc": "每小时检查课程提醒"
}
```

### **2. 页面生命周期中的频繁调用**
**问题**: 
- `schedule`页面每次`onShow`都调用多个云函数
- `index`页面每次显示都检查Badge状态
- 用户频繁切换页面导致重复调用

**影响**: 用户每次切换到课程表页面都会产生3-4次云函数调用

### **3. 缺乏缓存机制**
**问题**: 
- 相同的数据在短时间内重复请求
- Badge状态检查过于频繁
- 系统设置等稳定数据没有缓存

## 🛠️ **已实施的优化措施**

### **1. 缓存管理器**
✅ 创建了`cache-manager.js`，提供：
- 智能缓存机制，避免重复调用
- 不同数据类型的差异化缓存时间
- 自动过期清理
- 强制刷新选项

**缓存策略**:
```javascript
UNREAD_COUNT: 2分钟      // Badge状态
NEW_COURSES: 3分钟       // 新课程检查  
SYSTEM_SETTINGS: 10分钟  // 系统设置
SCHEDULE_DATA: 2分钟     // 课程表数据
```

### **2. 页面生命周期优化**
✅ `schedule`页面优化：
- 新课程检查改为3分钟间隔
- 数据刷新改为2分钟间隔
- 系统设置使用缓存加载

✅ `index`页面优化：
- Badge检查使用缓存机制
- 减少重复的云函数调用

### **3. 定时器配置修复**
✅ 修复了`courseReminder`定时器配置

## 📈 **预期优化效果**

### **调用次数减少预估**
- **页面切换优化**: 减少60-70%的重复调用
- **缓存机制**: 减少40-50%的数据请求
- **总体预期**: 减少50-60%的云函数调用次数

### **用户体验提升**
- 页面加载速度提升
- 减少网络请求，节省流量
- 更流畅的交互体验

## 🚀 **进一步优化建议**

### **1. 数据库查询优化**
**当前问题**: 
- 相册管理页面有大量的数据库直接查询
- 某些查询可能存在N+1问题

**建议**:
```javascript
// 优化前：多次查询
for (const folder of folders) {
  const images = await db.collection('album_images')
    .where({ folderId: folder._id }).get();
}

// 优化后：批量查询
const allImages = await db.collection('album_images')
  .where({ folderId: db.command.in(folderIds) }).get();
```

### **2. 实现数据预加载**
**建议**: 在应用启动时预加载常用数据
```javascript
// app.js中添加
async preloadData() {
  // 预加载系统设置
  await cachedCall.getSystemSettings();
  // 预加载用户信息
  if (this.isLoggedIn()) {
    await cachedCall.getUnreadCount(this.getUserInfo().openid);
  }
}
```

### **3. 批量操作优化**
**建议**: 将多个单独的数据库操作合并为批量操作
```javascript
// 优化前：多次单独操作
for (const item of items) {
  await db.collection('items').add(item);
}

// 优化后：批量操作
await db.collection('items').add(items);
```

### **4. 定时任务优化**
**当前配置**:
- `dailyReminder`: 每天20:00执行
- `courseReminder`: 每小时执行 ⚠️
- `cleanExpiredNotifications`: 每天02:00执行

**建议优化**:
```json
{
  "name": "courseReminder",
  "config": "0 0 */3 * * *",
  "desc": "每3小时检查课程提醒"
}
```

### **5. 监控和告警**
**建议**: 添加调用次数监控
```javascript
// 在关键位置添加调用次数统计
const callCounter = {
  daily: 0,
  monthly: 0,
  logCall(functionName) {
    this.daily++;
    this.monthly++;
    console.log(`云函数调用: ${functionName}, 今日: ${this.daily}`);
    
    // 告警机制
    if (this.daily > 500) {
      console.warn('⚠️ 今日调用次数过高!');
    }
  }
};
```

## 📋 **实施计划**

### **阶段一：立即实施** ✅
- [x] 修复定时器配置
- [x] 实施缓存管理器
- [x] 优化页面生命周期

### **阶段二：短期优化** (1-2周)
- [ ] 数据库查询优化
- [ ] 批量操作改造
- [ ] 数据预加载机制

### **阶段三：长期监控** (持续)
- [ ] 调用次数监控
- [ ] 性能数据分析
- [ ] 持续优化调整

## 🎯 **成功指标**

### **量化目标**
- 月调用次数控制在12万次以内（60%配额）
- 日均调用次数控制在4000次以内
- 页面加载时间减少30%

### **监控方式**
1. 微信开发者工具中的云开发控制台
2. 自定义的调用次数统计
3. 用户反馈和体验评估

## ⚠️ **注意事项**

### **缓存一致性**
- 确保缓存数据的时效性
- 重要操作后及时清除相关缓存
- 提供强制刷新机制

### **降级策略**
- 缓存失败时自动降级到直接调用
- 网络异常时的错误处理
- 用户体验不能因优化而降低

### **测试验证**
- 充分测试缓存机制的正确性
- 验证数据一致性
- 确保所有功能正常工作

---

**总结**: 通过实施缓存机制、优化页面生命周期和修复配置问题，预计可以将云函数调用次数减少50-60%，有效控制在配额范围内，同时提升用户体验。
