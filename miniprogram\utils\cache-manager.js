// cache-manager.js
// 云函数调用缓存管理器，减少重复调用，优化性能

/**
 * 缓存管理器类
 * 
 * 功能说明：
 * 1. 缓存云函数调用结果，避免短时间内重复调用
 * 2. 支持设置缓存过期时间
 * 3. 自动清理过期缓存
 * 4. 提供强制刷新机制
 * 
 * 使用场景：
 * - Badge状态检查（未读消息、新课程等）
 * - 系统设置获取
 * - 用户信息获取
 * - 课程列表等相对稳定的数据
 */
class CacheManager {
  constructor() {
    this.cache = new Map(); // 使用Map存储缓存数据
    this.defaultTTL = 5 * 60 * 1000; // 默认缓存5分钟
  }

  /**
   * 生成缓存键
   * @param {string} functionName 云函数名称
   * @param {string} action 操作类型
   * @param {Object} data 请求数据
   * @returns {string} 缓存键
   */
  generateKey(functionName, action, data = {}) {
    // 将参数序列化为字符串作为缓存键
    const keyData = {
      functionName,
      action,
      data: JSON.stringify(data)
    };
    return JSON.stringify(keyData);
  }

  /**
   * 获取缓存数据
   * @param {string} key 缓存键
   * @returns {Object|null} 缓存的数据，如果不存在或已过期返回null
   */
  get(key) {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > cached.expireTime) {
      this.cache.delete(key);
      return null;
    }

    console.log('🎯 使用缓存数据:', key);
    return cached.data;
  }

  /**
   * 设置缓存数据
   * @param {string} key 缓存键
   * @param {Object} data 要缓存的数据
   * @param {number} ttl 缓存时间（毫秒），默认使用defaultTTL
   */
  set(key, data, ttl = this.defaultTTL) {
    const expireTime = Date.now() + ttl;
    this.cache.set(key, {
      data,
      expireTime,
      createTime: Date.now()
    });
    console.log('💾 缓存数据:', key, `TTL: ${ttl}ms`);
  }

  /**
   * 删除指定缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    this.cache.delete(key);
    console.log('🗑️ 删除缓存:', key);
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear();
    console.log('🧹 清空所有缓存');
  }

  /**
   * 清理过期缓存
   */
  cleanExpired() {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, cached] of this.cache.entries()) {
      if (now > cached.expireTime) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 个过期缓存`);
    }
  }

  /**
   * 带缓存的云函数调用
   * @param {Object} options 调用参数
   * @param {string} options.name 云函数名称
   * @param {Object} options.data 调用数据
   * @param {number} options.ttl 缓存时间（毫秒）
   * @param {boolean} options.forceRefresh 是否强制刷新
   * @returns {Promise<Object>} 云函数调用结果
   */
  async callFunction(options) {
    const { name, data = {}, ttl = this.defaultTTL, forceRefresh = false } = options;
    const { action } = data;
    
    // 生成缓存键
    const cacheKey = this.generateKey(name, action, data.data);
    
    // 如果不强制刷新，先尝试从缓存获取
    if (!forceRefresh) {
      const cached = this.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      console.log('☁️ 调用云函数:', name, action);
      
      // 调用云函数
      const result = await wx.cloud.callFunction({
        name,
        data
      });

      // 只缓存成功的结果
      if (result.result && result.result.success) {
        this.set(cacheKey, result, ttl);
      }

      return result;
    } catch (error) {
      console.error('云函数调用失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;
    
    for (const [key, cached] of this.cache.entries()) {
      if (now > cached.expireTime) {
        expiredCount++;
      } else {
        validCount++;
      }
    }
    
    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount
    };
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager();

// 定期清理过期缓存（每5分钟）
setInterval(() => {
  cacheManager.cleanExpired();
}, 5 * 60 * 1000);

/**
 * 预定义的缓存配置
 * 根据数据更新频率设置不同的缓存时间
 */
export const CACHE_CONFIG = {
  // Badge相关 - 缓存时间较短，因为需要及时更新
  UNREAD_COUNT: 2 * 60 * 1000,      // 2分钟
  NEW_COURSES: 3 * 60 * 1000,       // 3分钟
  
  // 系统设置 - 缓存时间较长，因为变更不频繁
  SYSTEM_SETTINGS: 10 * 60 * 1000,  // 10分钟
  
  // 用户信息 - 中等缓存时间
  USER_INFO: 5 * 60 * 1000,         // 5分钟
  
  // 课程数据 - 根据业务需求调整
  COURSE_LIST: 3 * 60 * 1000,       // 3分钟
  SCHEDULE_DATA: 2 * 60 * 1000,     // 2分钟
};

/**
 * 便捷的缓存调用方法
 */
export const cachedCall = {
  /**
   * 获取未读消息数量（带缓存）
   */
  async getUnreadCount(userId, forceRefresh = false) {
    return cacheManager.callFunction({
      name: 'notificationManagement',
      data: {
        action: 'getUnreadCount',
        data: { userId }
      },
      ttl: CACHE_CONFIG.UNREAD_COUNT,
      forceRefresh
    });
  },

  /**
   * 检查新课程（带缓存）
   */
  async checkNewCourses(userId, forceRefresh = false) {
    return cacheManager.callFunction({
      name: 'notificationManagement',
      data: {
        action: 'checkNewCourses',
        data: { userId }
      },
      ttl: CACHE_CONFIG.NEW_COURSES,
      forceRefresh
    });
  },

  /**
   * 获取系统设置（带缓存）
   */
  async getSystemSettings(forceRefresh = false) {
    return cacheManager.callFunction({
      name: 'adminManagement',
      data: {
        action: 'getSystemSettings'
      },
      ttl: CACHE_CONFIG.SYSTEM_SETTINGS,
      forceRefresh
    });
  },

  /**
   * 获取课程表数据（带缓存）
   */
  async getScheduleData(userId, forceRefresh = false) {
    return cacheManager.callFunction({
      name: 'bookingManagement',
      data: {
        action: 'getScheduleData',
        data: { userId }
      },
      ttl: CACHE_CONFIG.SCHEDULE_DATA,
      forceRefresh
    });
  }
};

export default cacheManager;
